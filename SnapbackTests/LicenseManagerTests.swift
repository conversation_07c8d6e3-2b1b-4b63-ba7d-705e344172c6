import XCTest
@testable import Snapback

/// Tests for LicenseManager functionality
class LicenseManagerTests: XCTestCase {
    
    var licenseManager: LicenseManager!
    
    override func setUp() {
        super.setUp()
        
        // Clear UserDefaults before each test
        clearAllLicenseData()
        
        // Create a fresh instance for testing
        licenseManager = LicenseManager.shared
    }
    
    override func tearDown() {
        // Clean up after each test
        clearAllLicenseData()
        super.tearDown()
    }
    
    // MARK: - Helper Methods
    
    private func clearAllLicenseData() {
        let keys = [
            "SnapbackLicenseKey",
            "SnapbackUserEmail", 
            "SnapbackLicenseStatus",
            "SnapbackLicenseInfo",
            "SnapbackLastLicenseValidation",
            "SnapbackTrialStartDate",
            "SnapbackTrialEndDate",
            "SnapbackFirstLaunchDate",
            "SnapbackTrialChecksum"
        ]
        
        for key in keys {
            UserDefaults.standard.removeObject(forKey: key)
        }
        UserDefaults.standard.synchronize()
    }
    
    // MARK: - Initial State Tests
    
    func testInitialUnlicensedState() {
        // Given: Fresh license manager
        // When: Checking initial state
        // Then: Should be unlicensed with empty data
        XCTAssertEqual(licenseManager.licenseStatus, .unlicensed)
        XCTAssertTrue(licenseManager.licenseKey.isEmpty)
        XCTAssertTrue(licenseManager.userEmail.isEmpty)
        XCTAssertNil(licenseManager.licenseInfo)
        XCTAssertFalse(licenseManager.isTrialActive)
        XCTAssertEqual(licenseManager.remainingTrialDays, 0)
        XCTAssertFalse(licenseManager.hasFullAccess)
    }
    
    // MARK: - Trial Lifecycle Tests
    
    func testTrialActivation() async {
        // Given: Unlicensed state
        XCTAssertEqual(licenseManager.licenseStatus, .unlicensed)
        
        // When: Requesting trial license
        await licenseManager.requestTrialLicense(email: "<EMAIL>")
        
        // Then: Should have active trial
        XCTAssertEqual(licenseManager.licenseStatus, .trial)
        XCTAssertEqual(licenseManager.userEmail, "<EMAIL>")
        XCTAssertFalse(licenseManager.licenseKey.isEmpty)
        XCTAssertTrue(licenseManager.licenseKey.hasPrefix("TRIAL-"))
        XCTAssertTrue(licenseManager.isTrialActive)
        XCTAssertGreaterThan(licenseManager.remainingTrialDays, 0)
        XCTAssertTrue(licenseManager.hasFullAccess)
        XCTAssertNotNil(licenseManager.licenseInfo)
        XCTAssertEqual(licenseManager.licenseInfo?.licenseType, "Trial")
    }
    
    func testTrialDaysCalculation() async {
        // Given: Active trial
        await licenseManager.requestTrialLicense(email: "<EMAIL>")
        
        // When: Checking trial days
        let remainingDays = licenseManager.remainingTrialDays
        
        // Then: Should have reasonable number of days (14-15 for new trial)
        XCTAssertGreaterThanOrEqual(remainingDays, 14)
        XCTAssertLessThanOrEqual(remainingDays, 15)
    }
    
    // MARK: - License Key Tests
    
    func testValidLicenseKey() async {
        // Given: Valid license key
        let validKey = "snapback2025life"
        let email = "<EMAIL>"
        
        // When: Setting license key
        await licenseManager.setLicenseKeyAndEmail(validKey, email: email)
        
        // Then: Should be valid license
        XCTAssertEqual(licenseManager.licenseStatus, .valid)
        XCTAssertEqual(licenseManager.licenseKey, validKey.uppercased())
        XCTAssertEqual(licenseManager.userEmail, email)
        XCTAssertTrue(licenseManager.hasFullAccess)
        XCTAssertNotNil(licenseManager.licenseInfo)
        XCTAssertEqual(licenseManager.licenseInfo?.licenseType, "Lifetime License")
    }
    
    func testInvalidLicenseKey() async {
        // Given: Invalid license key
        let invalidKey = "invalid-key-123"
        let email = "<EMAIL>"
        
        // When: Setting invalid license key
        await licenseManager.setLicenseKeyAndEmail(invalidKey, email: email)
        
        // Then: Should be invalid license
        XCTAssertEqual(licenseManager.licenseStatus, .invalid)
        XCTAssertFalse(licenseManager.hasFullAccess)
        XCTAssertNotNil(licenseManager.lastError)
    }
    
    // MARK: - License Clearing Tests
    
    func testClearLicenseFromTrial() async {
        // Given: Active trial
        await licenseManager.requestTrialLicense(email: "<EMAIL>")
        XCTAssertEqual(licenseManager.licenseStatus, .trial)
        XCTAssertTrue(licenseManager.isTrialActive)
        
        // When: Clearing license
        licenseManager.clearLicense()
        
        // Then: Should be completely cleared
        XCTAssertEqual(licenseManager.licenseStatus, .unlicensed)
        XCTAssertTrue(licenseManager.licenseKey.isEmpty)
        XCTAssertTrue(licenseManager.userEmail.isEmpty)
        XCTAssertNil(licenseManager.licenseInfo)
        XCTAssertFalse(licenseManager.isTrialActive)
        XCTAssertEqual(licenseManager.remainingTrialDays, 0)
        XCTAssertNil(licenseManager.trialStartDate)
        XCTAssertNil(licenseManager.trialEndDate)
        XCTAssertFalse(licenseManager.hasFullAccess)
    }
    
    func testClearLicenseFromValid() async {
        // Given: Valid license
        await licenseManager.setLicenseKeyAndEmail("snapback2025life", email: "<EMAIL>")
        XCTAssertEqual(licenseManager.licenseStatus, .valid)
        
        // When: Clearing license
        licenseManager.clearLicense()
        
        // Then: Should be completely cleared
        XCTAssertEqual(licenseManager.licenseStatus, .unlicensed)
        XCTAssertTrue(licenseManager.licenseKey.isEmpty)
        XCTAssertTrue(licenseManager.userEmail.isEmpty)
        XCTAssertNil(licenseManager.licenseInfo)
        XCTAssertFalse(licenseManager.hasFullAccess)
    }
    
    // MARK: - Persistence Tests
    
    func testTrialPersistence() async {
        // Given: Active trial
        await licenseManager.requestTrialLicense(email: "<EMAIL>")
        let originalKey = licenseManager.licenseKey
        let originalEmail = licenseManager.userEmail
        let originalStatus = licenseManager.licenseStatus
        
        // When: Creating new instance (simulating app restart)
        let newManager = LicenseManager.shared
        
        // Then: Should restore trial state
        XCTAssertEqual(newManager.licenseKey, originalKey)
        XCTAssertEqual(newManager.userEmail, originalEmail)
        XCTAssertEqual(newManager.licenseStatus, originalStatus)
        XCTAssertTrue(newManager.isTrialActive)
        XCTAssertGreaterThan(newManager.remainingTrialDays, 0)
    }
    
    // MARK: - Complete Lifecycle Test
    
    func testCompleteLicenseLifecycle() async {
        // 1. Start unlicensed
        XCTAssertEqual(licenseManager.licenseStatus, .unlicensed)
        XCTAssertFalse(licenseManager.hasFullAccess)
        
        // 2. Activate trial
        await licenseManager.requestTrialLicense(email: "<EMAIL>")
        XCTAssertEqual(licenseManager.licenseStatus, .trial)
        XCTAssertTrue(licenseManager.hasFullAccess)
        XCTAssertTrue(licenseManager.licenseKey.hasPrefix("TRIAL-"))
        
        // 3. Upgrade to paid license
        await licenseManager.setLicenseKeyAndEmail("snapback2025life", email: "<EMAIL>")
        XCTAssertEqual(licenseManager.licenseStatus, .valid)
        XCTAssertTrue(licenseManager.hasFullAccess)
        XCTAssertEqual(licenseManager.licenseKey, "SNAPBACK2025LIFE")
        
        // 4. Clear license
        licenseManager.clearLicense()
        XCTAssertEqual(licenseManager.licenseStatus, .unlicensed)
        XCTAssertFalse(licenseManager.hasFullAccess)
        XCTAssertTrue(licenseManager.licenseKey.isEmpty)
        XCTAssertTrue(licenseManager.userEmail.isEmpty)
        XCTAssertNil(licenseManager.licenseInfo)
        XCTAssertFalse(licenseManager.isTrialActive)
        XCTAssertEqual(licenseManager.remainingTrialDays, 0)
    }
}
