# License Settings Module

This module provides comprehensive license management functionality for the Snapback macOS app, following the established UI design patterns and architecture.

## Overview

The License settings module consists of:

- **LicenseManager**: Core service for license validation and storage
- **LicenseSettingsView**: SwiftUI interface following Snapback's design system
- **Integration**: Seamless integration with the existing settings architecture

## Features

### 🔑 License Management

- License key input with auto-formatting (XXXX-XXXX-XXXX-XXXX)
- Real-time validation with loading states
- Persistent storage using UserDefaults
- Error handling with user-friendly messages

### 📊 License Status Display

- Visual status indicators with appropriate colors
- Detailed license information (type, user, expiration)
- Feature list for valid licenses
- Expiration warnings for time-limited licenses

### 🎨 UI Design

- Follows SnapbackTheme design system
- Consistent with General tab styling
- Uses established form patterns and spacing
- Includes appropriate SF Symbols icons

### 🔧 Integration

- Seamlessly integrated into existing settings tabs
- Uses SettingsFooter for consistent footer styling
- Follows established logging patterns
- Compatible with existing UserDefaults structure

## Demo License Key

For testing purposes, the following demo license key is available:

| License Key        | Type                   | Status           | Features                                                                                           |
| ------------------ | ---------------------- | ---------------- | -------------------------------------------------------------------------------------------------- |
| `snapback2025life` | Lifetime License       | Valid (Lifetime) | All Workspace Features, Unlimited Workspaces, Window Management, Keyboard Shortcuts, CloudKit Sync |
| `snapback2025year` | Annual License         | Valid (Expiring) | All Workspace Features, Unlimited Workspaces, Window Management, Keyboard Shortcuts, CloudKit Sync |
| `testexpiredlic`   | Expired Annual License | Expired          | Used for testing expired paid license scenarios                                                    |
| `testexprtrial123` | Expired Trial          | Expired          | Used for testing trial expiration alert and restricted access                                      |

All other keys will be marked as invalid.

## Architecture

### LicenseManager

```swift
class LicenseManager: ObservableObject {
    // Published properties for UI binding
    @Published var licenseStatus: LicenseStatus
    @Published var licenseKey: String
    @Published var licenseInfo: LicenseInfo?

    // Core methods
    func validateLicense(silent: Bool = false) async
    func setLicenseKey(_ key: String) async
    func clearLicense()
}
```

### LicenseStatus

```swift
enum LicenseStatus: String, CaseIterable {
    case unlicensed = "unlicensed"
    case trial = "trial"
    case valid = "valid"
    case invalid = "invalid"
    case expired = "expired"
}
```

### LicenseInfo

```swift
struct LicenseInfo: Codable {
    let licenseType: String
    let registeredUser: String
    let expirationDate: Date?
    let features: [String]
}
```

## Usage

### Adding to Settings

The License tab is automatically included in the settings interface:

```swift
// In SettingsView.swift
tabList.append(Tab(id: "license", title: "License", icon: "key"))

// In content switch
case "license":
    LicenseSettingsView()
```

### Accessing License Manager

```swift
// Get shared instance
let licenseManager = LicenseManager.shared

// Check license status
if licenseManager.licenseStatus == .valid {
    // License is valid
}

// Validate a lifetime license key
await licenseManager.setLicenseKey("snapback2025life")

// Test annual license key
await licenseManager.setLicenseKey("snapback2025year")

// Test expired paid license scenario
await licenseManager.setLicenseKey("testexpiredlic")

// Test expired trial scenario
await licenseManager.setLicenseKey("testexprtrial123")
```

## Styling Consistency

The module follows Snapback's established design patterns:

### Theme Usage

- `SnapbackTheme.Background.card` for GroupBox backgrounds
- `SnapbackTheme.Text.error` for error messages
- `SnapbackTheme.Padding.section` for consistent spacing
- `snapbackSectionTitleStyle()` for section headers
- `snapbackRowStyle()` for form rows

### Icons

- `key` for the License tab icon
- `checkmark.circle.fill` for valid status
- `xmark.circle.fill` for invalid status
- `clock` for trial status
- `clock.badge.exclamationmark` for expired status
- `exclamationmark.triangle` for unlicensed status

### Layout

- Follows the same ScrollView + VStack pattern as General settings
- Uses GroupBox for section containers
- Implements SettingsFooter for consistent footer styling
- Maintains proper spacing and visual hierarchy

## Logging

The module integrates with Snapback's logging system:

```swift
private let logger = LoggingService.shared
private let serviceName = "LicenseManager"

// Example logging
logger.info("License validation successful", service: serviceName)
logger.warning("License validation failed: invalid key", service: serviceName)
logger.error("License validation error: \(error)", service: serviceName)
```

## Persistence

License information is stored using UserDefaults with consistent key naming:

- `SnapbackLicenseKey`: The license key
- `SnapbackLicenseStatus`: Current license status
- `SnapbackLicenseInfo`: Encoded license information
- `SnapbackLastLicenseValidation`: Timestamp of last validation

## Future Enhancements

Potential improvements for production use:

1. **Real API Integration**: Replace placeholder validation with actual license server
2. **Offline Validation**: Add cryptographic license validation for offline use
3. **Auto-Renewal**: Implement automatic license renewal for subscription licenses
4. **License Transfer**: Add functionality to transfer licenses between devices
5. **Usage Analytics**: Track feature usage based on license type
6. **Grace Period**: Implement grace period for expired licenses

## Testing

To test the license functionality:

1. Open Snapback Settings
2. Navigate to the License tab
3. Enter the demo license key
4. Observe the validation process and status updates
5. Test different license types and statuses
6. Verify persistence by restarting the app

The module provides comprehensive license management while maintaining consistency with Snapback's existing design and architecture patterns.
