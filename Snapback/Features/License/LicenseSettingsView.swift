import SwiftUI

struct LicenseSettingsView: View {
    @StateObject private var licenseManager = LicenseManager.shared
    @State private var licenseKeyInput: String = ""
    @State private var emailInput: String = ""
    @State private var showingClearConfirmation = false

    var body: some View {
        VStack(spacing: 0) {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // License Status Section
                    Text("License Status")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            // Current Status Row
                            HStack {
                                Image(systemName: statusIcon)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .frame(width: 16)

                                Text("Status")

                                Spacer()

                                Text(licenseManager.licenseStatus.displayName)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium))
                            }
                            .snapbackRowStyle()

                            // Trial Information (if on active trial)
                            if licenseManager.licenseStatus == .trial
                                && licenseManager.isTrialActive
                            {
                                Divider()

                                HStack {
                                    Image(systemName: "clock")
                                        .foregroundColor(.orange)
                                        .frame(width: 16)

                                    Text("Trial Days Remaining")

                                    Spacer()

                                    Text("\(licenseManager.remainingTrialDays) days")
                                        .foregroundColor(.orange)
                                        .font(
                                            .system(
                                                size: SnapbackTheme.FontSize.body, weight: .medium))
                                }
                                .snapbackRowStyle()
                            }

                            // License Information (if available)
                            if let info = licenseManager.licenseInfo {
                                Divider()

                                VStack(spacing: 0) {
                                    // License Type
                                    HStack {
                                        Image(systemName: "doc.text")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("License Type")

                                        Spacer()

                                        Text(info.licenseType)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    Divider()

                                    // Registered User
                                    HStack {
                                        Image(systemName: "person")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Registered To")

                                        Spacer()

                                        Text(info.registeredUser)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    Divider()

                                    // Expiration Date
                                    HStack {
                                        Image(systemName: "calendar")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Expires")

                                        Spacer()

                                        Text(info.expirationDisplayText)
                                            .foregroundColor(info.isExpired ? .red : .secondary)
                                    }
                                    .snapbackRowStyle()
                                }
                            }
                        }
                    }

                    // License Key Section
                    Text("License Information")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            // Email Input Row
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("Email Address")

                                    Spacer()

                                    if licenseManager.isValidating {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                    }
                                }

                                TextField("<EMAIL>", text: $emailInput)
                                    .textFieldStyle(.roundedBorder)
                                    .disabled(licenseManager.isValidating)
                                    .disableAutocorrection(true)
                                    .textContentType(.emailAddress)
                            }
                            .snapbackRowStyle()

                            Divider()

                            // License Key Input Row
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("License Key")

                                    Spacer()
                                }

                                TextField("XXXX-XXXX-XXXX-XXXX", text: $licenseKeyInput)
                                    .textFieldStyle(.roundedBorder)
                                    .font(.system(.body, design: .monospaced))
                                    .disabled(licenseManager.isValidating)
                                    .disableAutocorrection(true)
                                    .onSubmit {
                                        Task {
                                            await licenseManager.setLicenseKeyAndEmail(
                                                licenseKeyInput, email: emailInput)
                                        }
                                    }
                                    .onChange(of: licenseKeyInput) { oldValue, newValue in
                                        // Auto-format license key with dashes
                                        let cleaned = newValue.replacingOccurrences(
                                            of: "-", with: ""
                                        ).uppercased()
                                        if cleaned.count <= 16 {
                                            let formatted = formatLicenseKey(cleaned)
                                            if formatted != newValue {
                                                licenseKeyInput = formatted
                                            }
                                        } else {
                                            licenseKeyInput = oldValue
                                        }
                                    }

                                // Error Message
                                if let error = licenseManager.lastError {
                                    Text(error)
                                        .snapbackCaptionStyle()
                                        .foregroundColor(.red)
                                }

                                // Action Buttons
                                HStack(spacing: 12) {
                                    Button("Validate License") {
                                        Task {
                                            await licenseManager.setLicenseKeyAndEmail(
                                                licenseKeyInput, email: emailInput)
                                        }
                                    }
                                    .buttonStyle(.bordered)
                                    .disabled(
                                        licenseKeyInput.isEmpty || emailInput.isEmpty
                                            || licenseManager.isValidating)

                                    // Clear Button (only show if there's a license)
                                    if !licenseManager.licenseKey.isEmpty {
                                        Button("Clear License") {
                                            showingClearConfirmation = true
                                        }
                                        .buttonStyle(.bordered)
                                        .foregroundColor(.red)
                                    }

                                    Spacer()
                                }
                                .padding(.top, 8)
                            }
                            .snapbackRowStyle()
                        }
                    }

                    // Features Section (if license is valid)
                    if let info = licenseManager.licenseInfo, licenseManager.licenseStatus == .valid
                    {
                        Text("Licensed Features")
                            .snapbackSectionTitleStyle()

                        GroupBox {
                            VStack(spacing: 0) {
                                ForEach(Array(info.features.enumerated()), id: \.offset) {
                                    index, feature in
                                    HStack {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.green)
                                            .frame(width: 16)

                                        Text(feature)

                                        Spacer()
                                    }
                                    .snapbackRowStyle()

                                    if index < info.features.count - 1 {
                                        Divider()
                                    }
                                }
                            }
                        }
                    }

                    // Help Section
                    Text("Help")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Need help with licensing?")
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium))

                                Text(
                                    "Contact support for license issues, visit our website for purchasing options, or check your email for license key delivery."
                                )
                                .snapbackCaptionStyle()

                                HStack(spacing: 12) {
                                    Button("Contact Support") {
                                        if let url = URL(string: "mailto:<EMAIL>") {
                                            NSWorkspace.shared.open(url)
                                        }
                                    }
                                    .buttonStyle(.bordered)

                                    Button("Purchase License") {
                                        if let url = URL(string: "https://snapbackapp.com/purchase")
                                        {
                                            NSWorkspace.shared.open(url)
                                        }
                                    }
                                    .buttonStyle(.bordered)

                                    Spacer()
                                }
                                .padding(.top, 8)
                            }
                            .snapbackRowStyle()
                        }
                    }

                    Spacer(minLength: 20)
                }
                .padding()
            }

            // Footer with Reset Button
            SettingsFooter(buttonTitle: "Reset License Settings") {
                showingClearConfirmation = true
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            // Load current license key and email into input fields
            licenseKeyInput = licenseManager.licenseKey
            emailInput = licenseManager.userEmail
        }
        .alert("Clear License", isPresented: $showingClearConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Clear", role: .destructive) {
                licenseManager.clearLicense()
                licenseKeyInput = ""
            }
        } message: {
            Text(
                "Are you sure you want to clear the current license? This action cannot be undone.")
        }
    }

    // MARK: - Helper Properties

    private var statusIcon: String {
        switch licenseManager.licenseStatus {
        case .unlicensed:
            return "exclamationmark.triangle"
        case .valid:
            return "checkmark.circle.fill"
        case .invalid:
            return "xmark.circle.fill"
        case .expired:
            return "clock.badge.exclamationmark"
        case .trial:
            return "clock"
        }

    }
}

// MARK: - Helper Methods

/// Format license key with dashes (XXXX-XXXX-XXXX-XXXX)
private func formatLicenseKey(_ key: String) -> String {
    let cleaned = key.replacingOccurrences(of: "-", with: "")
    var formatted = ""

    for (index, character) in cleaned.enumerated() {
        if index > 0 && index % 4 == 0 {
            formatted += "-"
        }
        formatted += String(character)
    }

    return formatted
}

#Preview {
    LicenseSettingsView()
        .frame(width: 600, height: 500)
        .background(SnapbackTheme.Background.window)
}
