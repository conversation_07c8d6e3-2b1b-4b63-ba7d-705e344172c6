import Cocoa
import SwiftUI

/// Window controller for the trial email collection modal
class TrialEmailWindowController: NSWindowController, NSWindowDelegate {
    private var onCompletion: (() -> Void)?

    convenience init(onCompletion: @escaping () -> Void) {
        // Create the window
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 450, height: 500),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )

        // Configure window
        window.title = "Start Free Trial"
        window.isReleasedWhenClosed = false
        window.center()
        window.level = .modalPanel
        window.isMovableByWindowBackground = true

        // Initialize with the window
        self.init(window: window)
        self.onCompletion = onCompletion

        // Create the SwiftUI view
        let trialView = TrialEmailCollectionView(
            onDismiss: { [weak self] in
                self?.closeModal()
            },
            onSuccess: { [weak self] in
                self?.closeModal()
            }
        )

        // Set up the hosting view
        let hostingView = NSHostingView(rootView: trialView)
        window.contentView = hostingView

        // Make window non-resizable
        window.styleMask.remove(.resizable)

        // Set minimum and maximum size to prevent resizing
        window.minSize = NSSize(width: 450, height: 500)
        window.maxSize = NSSize(width: 450, height: 500)

        // Set window delegate to receive close notifications
        window.delegate = self
    }

    /// Show the modal window
    func showModal() {
        guard let window = window else { return }

        // Center the window
        window.center()

        // Show the window
        window.makeKeyAndOrderFront(nil)

        // Bring to front
        NSApp.activate(ignoringOtherApps: true)
    }

    /// Close the modal window
    private func closeModal() {
        window?.close()
        onCompletion?()
    }

    /// Handle window closing
    func windowWillClose(_ notification: Notification) {
        onCompletion?()
    }
}

// MARK: - WindowManager Extension

extension WindowManager {
    /// Open the trial email collection modal
    func openTrialEmailCollection(onCompletion: @escaping () -> Void = {}) {
        let windowController = TrialEmailWindowController(onCompletion: onCompletion)
        windowController.showModal()

        // Keep a reference to prevent deallocation
        // Note: The window controller will be released when the window closes
        // due to the onCompletion callback
    }
}
