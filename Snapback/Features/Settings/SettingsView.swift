import AppKit  // For NSEvent
import Carbon.HIToolbox  // For key code constants
// Import for system shortcut conflict detection
import Foundation
import KeyboardShortcuts
import SwiftUI

struct SettingsView: View {
    @AppStorage("startAtLogin") private var startAtLogin = false
    @EnvironmentObject private var appDelegate: AppDelegate
    @EnvironmentObject private var workspaceService: WorkspaceService
    @State private var selectedTab = "general"

    // Track window management enabled state
    @State private var windowManagementEnabled = DefaultsManager.shared.windowManagementEnabled

    // Track license status for tab visibility
    @StateObject private var licenseManager = LicenseManager.shared

    // Define the tabs with their icons and titles - computed property to be dynamic
    private var tabs: [Tab] {
        // Check if user has full access (valid license or active trial)
        let hasFullAccess = licenseManager.hasFullAccess

        // If user doesn't have full access, only show License tab
        if !hasFullAccess {
            return [Tab(id: "license", title: "License", icon: "key")]
        }

        // Full access users see all tabs
        var tabList = [
            Tab(id: "general", title: "General", icon: "gearshape")
        ]

        // Only include Hotkeys tab if window management is enabled
        if windowManagementEnabled {
            tabList.append(Tab(id: "hotkeys", title: "Hotkeys", icon: "command"))
        }

        tabList.append(
            Tab(
                id: "workspaces", title: "Workspaces",
                icon: "inset.filled.topleft.topright.bottomleft.bottomright.rectangle"))

        // Add License tab
        tabList.append(Tab(id: "license", title: "License", icon: "key"))

        return tabList
    }

    // Get the current tab title for the window title
    private var currentTabTitle: String {
        tabs.first(where: { $0.id == selectedTab })?.title ?? "Settings"
    }

    // Validate that the selected tab is available for the current license status
    private func validateSelectedTab() {
        let availableTabIds = tabs.map { $0.id }

        // If current tab is not available, switch to the first available tab
        if !availableTabIds.contains(selectedTab) {
            if let firstTab = availableTabIds.first {
                selectedTab = firstTab
            }
        }
    }

    var body: some View {
        // We need to use NSViewControllerRepresentable to properly customize the window
        VStack(spacing: 0) {
            // Custom toolbar - this will be positioned at the top
            HStack(spacing: 0) {
                Spacer()
                ForEach(tabs) { tab in
                    ToolbarButton(
                        tab: tab,
                        isSelected: selectedTab == tab.id,
                        action: { selectedTab = tab.id }
                    )
                }
                Spacer()
            }
            .padding(.top, 8)
            .padding(.bottom, 4)

            // Bottom border
            Divider()

            // Content area
            Group {
                switch selectedTab {
                case "general":
                    GeneralSettingsView(
                        startAtLogin: $startAtLogin,
                        appDelegate: appDelegate
                    )
                case "hotkeys":
                    ShortcutSettingsView()
                        .environmentObject(workspaceService)
                case "workspaces":
                    WorkspacesSettingsView()
                        .environmentObject(workspaceService)
                case "license":
                    LicenseSettingsView()
                default:
                    EmptyView()
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .frame(width: 600, height: 500)
        .onAppear {
            // Only update the window title on appear, don't reconfigure the window
            // This avoids conflicts with the WindowManager configuration
            DispatchQueue.main.async {
                if let window = NSApp.mainWindow {
                    window.title = currentTabTitle
                }
            }

            // Update window management enabled state
            windowManagementEnabled = DefaultsManager.shared.windowManagementEnabled

            // Ensure selected tab is valid for current license status
            validateSelectedTab()

            // Add observer for window management enabled changes
            NotificationCenter.default.addObserver(
                forName: Notification.Name("WindowManagementEnabledChanged"),
                object: nil,
                queue: .main
            ) { notification in
                if let isEnabled = notification.object as? Bool {
                    windowManagementEnabled = isEnabled

                    // If hotkeys tab is currently selected and window management is disabled,
                    // switch to general tab
                    if selectedTab == "hotkeys" && !isEnabled {
                        selectedTab = "general"
                    }
                }
            }

            // Add observer for license status changes
            NotificationCenter.default.addObserver(
                forName: Notification.Name("LicenseStatusChanged"),
                object: nil,
                queue: .main
            ) { _ in
                // Validate selected tab when license status changes
                validateSelectedTab()
            }
        }
        .onChange(of: selectedTab) { oldValue, newValue in
            // Update the window title when tab changes
            // Use DispatchQueue.main.async to ensure it happens after the view update
            DispatchQueue.main.async {
                if let window = NSApp.mainWindow {
                    window.title = currentTabTitle
                }
            }
        }
    }
}

// Tab model
struct Tab: Identifiable {
    let id: String
    let title: String
    let icon: String
}

// Custom toolbar button
struct ToolbarButton: View {
    let tab: Tab
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: tab.icon)
                    .font(.system(size: 16))
                    .symbolRenderingMode(.hierarchical)
                    .foregroundColor(isSelected ? .accentColor : .secondary)
                    .frame(height: 18)
                    .imageScale(.large)

                Text(tab.title)
                    .font(.system(size: 11, weight: .regular))
                    .foregroundColor(isSelected ? .primary : .secondary)
            }
            .frame(width: 70, height: 40)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .background(Color.clear)
        .padding(.horizontal, 5)
        // No underline indicator for selected tab
    }
}

struct GeneralSettingsView: View {
    @Binding var startAtLogin: Bool
    let appDelegate: AppDelegate

    // Drag-to-snap settings
    @AppStorage("dragToSnapEnabled") private var dragToSnapEnabled = true
    @AppStorage("avoidSystemConflicts") private var avoidSystemConflicts = true
    @State private var snapModifiers: UInt = NSEvent.ModifierFlags.option.rawValue
    @State private var selectedModifier = 2  // Option (⌥) is at index 2 in modifierOptions

    // Track the allow any shortcut state to force UI updates
    @State private var allowAnyShortcut: Bool = false

    // State to trigger reset
    @State private var resetTriggered: Bool = false

    // Import/Export state
    @EnvironmentObject private var workspaceService: WorkspaceService
    @State private var importExportService = WorkspaceImportExportService()
    @State private var showingConflictDialog = false
    @State private var pendingImportResult: ImportResult?
    @State private var conflicts: [WorkspaceConflict] = []
    @State private var isProcessing = false

    // Save Workspace shortcut conflict detection
    @State private var saveWorkspaceConflictingShortcut: String? = nil
    @State private var showingSaveWorkspaceConflictAlert = false

    private let modifierOptions = [
        (0, "None"),
        (NSEvent.ModifierFlags.command.rawValue, "Command (⌘)"),
        (NSEvent.ModifierFlags.option.rawValue, "Option (⌥)"),
        (NSEvent.ModifierFlags.control.rawValue, "Control (⌃)"),
        (NSEvent.ModifierFlags.shift.rawValue, "Shift (⇧)"),
    ]

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // Application Settings
                Text("Application")
                    .snapbackSectionTitleStyle()

                GroupBox {
                    VStack(spacing: 0) {
                        HStack {
                            Text("Start at login")
                            Spacer()
                            Toggle("", isOn: $startAtLogin)
                                .toggleStyle(.switch)
                                .labelsHidden()
                                .onChange(of: startAtLogin) { oldValue, newValue in
                                    LoginItemManager.setStartAtLogin(newValue)
                                }
                        }
                        .snapbackRowStyle()

                        Divider()

                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Enable window management features")
                                Spacer()
                                Toggle(
                                    "",
                                    isOn: Binding(
                                        get: { DefaultsManager.shared.windowManagementEnabled },
                                        set: { newValue in
                                            DefaultsManager.shared.setWindowManagementEnabled(
                                                newValue)

                                            let logger = LoggingService.shared
                                            logger.info(
                                                "Window management features \(newValue ? "enabled" : "disabled")",
                                                service: "GeneralSettingsView",
                                                category: .general
                                            )

                                            // Post notification to update shortcuts and menu
                                            NotificationCenter.default.post(
                                                name: Notification.Name(
                                                    "WindowManagementEnabledChanged"),
                                                object: newValue
                                            )

                                            // Refresh the status menu to show/hide window management items
                                            NotificationCenter.default.post(
                                                name: Notification.Name("RefreshStatusMenu"),
                                                object: nil
                                            )
                                        }
                                    )
                                )
                                .toggleStyle(.switch)
                                .labelsHidden()
                            }

                            Text(
                                "When disabled, all window management shortcuts and menu items will be hidden. Only workspace save/restore functionality will remain active."
                            )
                            .snapbackCaptionStyle()
                        }
                        .snapbackRowStyle()
                    }
                }

                // Workspaces Settings
                Text("Workspaces")
                    .snapbackSectionTitleStyle()

                GroupBox {
                    VStack(spacing: 0) {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Close non-workspace windows when restoring")
                                Spacer()
                                Toggle(
                                    "",
                                    isOn: Binding(
                                        get: { DefaultsManager.shared.closeNonWorkspaceWindows },
                                        set: {
                                            DefaultsManager.shared.setCloseNonWorkspaceWindows($0)
                                        }
                                    )
                                )
                                .toggleStyle(.switch)
                                .labelsHidden()
                            }

                            Text(
                                "When enabled, windows that are not part of the workspace will be closed when restoring a workspace."
                            )
                            .snapbackCaptionStyle()
                        }
                        .snapbackRowStyle()
                    }
                }

                // Keyboard Shortcuts Settings
                Text("Keyboard Shortcuts")
                    .snapbackSectionTitleStyle()

                GroupBox {
                    VStack(spacing: 0) {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Allow any keyboard shortcut")
                                Spacer()
                                Toggle("", isOn: $allowAnyShortcut)
                                    .toggleStyle(.switch)
                                    .labelsHidden()
                                    .onChange(of: allowAnyShortcut) { oldValue, newValue in
                                        // Update both the validator and DefaultsManager
                                        ShortcutValidatorManager.shared.setAllowAnyShortcut(
                                            newValue)
                                        DefaultsManager.shared.setAllowAnyShortcut(newValue)
                                    }
                            }

                            Text(
                                "When enabled, Snapback will allow you to set any keyboard shortcut, even if it conflicts with system shortcuts or other applications."
                            )
                            .snapbackCaptionStyle()
                        }
                        .snapbackRowStyle()
                    }
                }

                // Drag-to-Snap Settings
                Text("Drag-to-Snap")
                    .snapbackSectionTitleStyle()

                GroupBox {
                    VStack(spacing: 0) {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Enable drag-to-snap")
                                Spacer()
                                Toggle("", isOn: $dragToSnapEnabled)
                                    .toggleStyle(.switch)
                                    .labelsHidden()
                                    .onChange(of: dragToSnapEnabled) { oldValue, newValue in
                                        // Post notification to update SnappingManager
                                        NotificationCenter.default.post(
                                            name: Notification.Name("DragToSnapEnabledChanged"),
                                            object: newValue
                                        )
                                    }
                            }

                            Text(
                                "When enabled, drag-to-snap allows you to snap windows by dragging them to the edges or corners of the screen."
                            )
                            .snapbackCaptionStyle()
                        }
                        .snapbackRowStyle()

                        Divider()

                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Modifier key required for drag-to-snap:")
                                Spacer()
                                Picker("", selection: $selectedModifier) {
                                    ForEach(0..<modifierOptions.count, id: \.self) { index in
                                        Text(modifierOptions[index].1).tag(index)
                                    }
                                }
                                .frame(width: 120)
                                .labelsHidden()
                                .onChange(of: selectedModifier) { oldValue, newValue in
                                    snapModifiers = modifierOptions[newValue].0
                                    UserDefaults.standard.set(
                                        snapModifiers, forKey: "snapModifiers")

                                    // Post notification to update SnappingManager
                                    NotificationCenter.default.post(
                                        name: Notification.Name("SnapModifiersChanged"),
                                        object: modifierOptions[newValue].0
                                    )
                                }
                            }

                            Text(
                                "If a modifier key is selected, you'll need to hold it while dragging windows to activate the snapping functionality."
                            )
                            .snapbackCaptionStyle()
                        }
                        .snapbackRowStyle()

                        Divider()

                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Avoid conflicts with macOS features")
                                Spacer()
                                Toggle("", isOn: $avoidSystemConflicts)
                                    .toggleStyle(.switch)
                                    .labelsHidden()
                                    .onChange(of: avoidSystemConflicts) { oldValue, newValue in
                                        // Post notification to update SnappingManager
                                        NotificationCenter.default.post(
                                            name: Notification.Name("AvoidSystemConflictsChanged"),
                                            object: newValue
                                        )
                                    }
                            }

                            Text(
                                "When 'Avoid conflicts with macOS features' is enabled, Snapback will detect and avoid potential conflicts with Mission Control, Stage Manager, and other macOS window management features."
                            )
                            .snapbackCaptionStyle()
                        }
                        .snapbackRowStyle()
                    }
                }

                // Save Workspace Shortcut Section
                Text("Save Workspace Shortcut")
                    .snapbackSectionTitleStyle()

                GroupBox {
                    VStack(spacing: 0) {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Save Workspace")
                                Spacer()
                                KeyboardShortcuts.Recorder(
                                    for: .saveWorkspace,
                                    onChange: { newShortcut in
                                        let logger = LoggingService.shared
                                        logger.debug(
                                            "Save Workspace shortcut changed",
                                            service: "GeneralSettingsView",
                                            category: .shortcuts
                                        )

                                        // Check for conflicts if there's a new shortcut
                                        if let shortcut = newShortcut {
                                            checkSaveWorkspaceConflicts(
                                                shortcut: shortcut, showAlert: true)
                                        } else {
                                            // Clear conflict state when shortcut is removed
                                            saveWorkspaceConflictingShortcut = nil
                                        }

                                        // Post notification to refresh menu
                                        NotificationCenter.default.post(
                                            name: Notification.Name("RefreshStatusMenu"),
                                            object: nil
                                        )
                                    }
                                )
                                .frame(width: 150)
                            }

                            Text(
                                "Set a keyboard shortcut to quickly save the current window layout as a new workspace."
                            )
                            .snapbackCaptionStyle()

                            // Show warning if there's a conflict
                            if let conflictName = saveWorkspaceConflictingShortcut {
                                Text("⚠️ This shortcut conflicts with \(conflictName)")
                                    .snapbackCaptionStyle()
                                    .foregroundColor(.orange)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .padding(.top, 4)
                            }
                        }
                        .snapbackRowStyle()
                    }
                }

                // Import/Export Section
                Text("Import & Export")
                    .snapbackSectionTitleStyle()

                GroupBox {
                    VStack(spacing: 0) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(
                                "Export your workspace configurations to create backups or share setups. Import previously exported workspace files to restore configurations."
                            )
                            .snapbackCaptionStyle()

                            HStack(spacing: 12) {
                                Button(action: importWorkspaces) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "arrow.down.circle")
                                            .font(.system(size: 14))
                                        Text("Import")
                                            .font(.system(size: 13))
                                    }
                                    .frame(maxWidth: .infinity)
                                }
                                .buttonStyle(.bordered)
                                .disabled(isProcessing)

                                Button(action: exportWorkspaces) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "arrow.up.circle")
                                            .font(.system(size: 14))
                                        Text("Export")
                                            .font(.system(size: 13))
                                    }
                                    .frame(maxWidth: .infinity)
                                }
                                .buttonStyle(.bordered)
                                .disabled(
                                    workspaceService.workspaces.isEmpty || isProcessing)
                            }
                            .padding(.top, 8)

                            if isProcessing {
                                HStack(spacing: 8) {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("Processing...")
                                        .font(.system(size: 12))
                                        .foregroundColor(.secondary)
                                }
                                .frame(maxWidth: .infinity, alignment: .center)
                                .padding(.top, 8)
                            }
                        }
                        .snapbackRowStyle()
                    }
                }

                // Defaults Section
                Text("Defaults")
                    .snapbackSectionTitleStyle()

                GroupBox {
                    Button("Restore to defaults") {
                        print("Reset to defaults button clicked")
                        resetTriggered = true
                    }
                    .buttonStyle(PlainButtonStyle())
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 8)
                }
            }
            .padding()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            // Sync the AppStorage value with the actual login item status when the view appears
            startAtLogin = LoginItemManager.isStartingAtLogin()

            // Load the snapModifiers value from UserDefaults
            if let modifierValue = UserDefaults.standard.object(forKey: "snapModifiers") as? UInt {
                snapModifiers = modifierValue
            } else {
                // Set default to Option (⌥) if not set
                snapModifiers = NSEvent.ModifierFlags.option.rawValue
                UserDefaults.standard.set(
                    NSEvent.ModifierFlags.option.rawValue, forKey: "snapModifiers")
            }

            // Set the selected modifier based on the stored value
            if let index = modifierOptions.firstIndex(where: { $0.0 == snapModifiers }) {
                selectedModifier = index
            } else {
                selectedModifier = 2  // Default to Option (⌥) at index 2
            }

            // Initialize the allowAnyShortcut state from DefaultsManager
            allowAnyShortcut = ShortcutValidatorManager.shared.allowAnyShortcut

            // Check for Save Workspace shortcut conflicts on appear
            if let shortcut = KeyboardShortcuts.getShortcut(for: .saveWorkspace) {
                checkSaveWorkspaceConflicts(shortcut: shortcut, showAlert: false)
            }
        }
        .onChange(of: resetTriggered) { oldValue, newValue in
            if newValue {
                print("Reset triggered via state change")
                resetToDefaults()
                // Reset the trigger after handling
                resetTriggered = false
            }
        }
        .alert("Import Conflicts Detected", isPresented: $showingConflictDialog) {
            Button("Overwrite Existing", role: .destructive) {
                resolveConflicts(strategy: .overwrite)
            }
            Button("Rename Imported") {
                resolveConflicts(strategy: .rename)
            }
            Button("Skip Conflicting", role: .cancel) {
                resolveConflicts(strategy: .skip)
            }
        } message: {
            Text(conflictMessage)
        }
        .alert("Shortcut Conflict", isPresented: $showingSaveWorkspaceConflictAlert) {
            Button("OK", role: .cancel) {}
        } message: {
            if let conflict = saveWorkspaceConflictingShortcut {
                Text(
                    "This shortcut is already assigned to \(conflict). Using the same shortcut for multiple actions may cause unexpected behavior."
                )
            } else {
                Text("This shortcut conflicts with another preset.")
            }
        }
    }

    // Reset all general settings to their default values
    private func resetToDefaults() {
        print("🔄 resetToDefaults() function called")

        let logger = LoggingService.shared
        logger.info(
            "Resetting general settings to defaults",
            service: "GeneralSettingsView",
            category: .general
        )

        // Reset Application settings
        startAtLogin = false
        LoginItemManager.setStartAtLogin(false)

        // Reset Window Management settings
        DefaultsManager.shared.setWindowManagementEnabled(true)
        NotificationCenter.default.post(
            name: Notification.Name("WindowManagementEnabledChanged"),
            object: true
        )

        // Reset Workspaces settings
        DefaultsManager.shared.setCloseNonWorkspaceWindows(false)

        // Reset Keyboard Shortcuts settings
        allowAnyShortcut = false
        ShortcutValidatorManager.shared.setAllowAnyShortcut(false)
        DefaultsManager.shared.setAllowAnyShortcut(false)

        // Reset Drag-to-Snap settings
        dragToSnapEnabled = true
        NotificationCenter.default.post(
            name: Notification.Name("DragToSnapEnabledChanged"),
            object: true
        )

        selectedModifier = 2  // Option (⌥) is at index 2 in modifierOptions
        snapModifiers = NSEvent.ModifierFlags.option.rawValue
        UserDefaults.standard.set(NSEvent.ModifierFlags.option.rawValue, forKey: "snapModifiers")
        NotificationCenter.default.post(
            name: Notification.Name("SnapModifiersChanged"),
            object: NSEvent.ModifierFlags.option.rawValue
        )

        avoidSystemConflicts = true
        NotificationCenter.default.post(
            name: Notification.Name("AvoidSystemConflictsChanged"),
            object: true
        )

        // Force synchronize to ensure changes are written immediately
        UserDefaults.standard.synchronize()

        logger.info(
            "General settings reset to defaults",
            service: "GeneralSettingsView",
            category: .general
        )

        print("🔄 resetToDefaults() function completed")
    }

    // MARK: - Computed Properties

    private var conflictMessage: String {
        let nameConflicts = conflicts.filter { $0.conflictType == .nameConflict }.count
        let shortcutConflicts = conflicts.filter { $0.conflictType == .shortcutConflict }.count

        var message = "Found conflicts with existing workspaces:\n"
        if nameConflicts > 0 {
            message += "• \(nameConflicts) workspace(s) with duplicate names\n"
        }
        if shortcutConflicts > 0 {
            message += "• \(shortcutConflicts) workspace(s) with duplicate shortcuts\n"
        }
        message += "\nHow would you like to resolve these conflicts?"

        return message
    }

    // MARK: - Import/Export Methods

    private func exportWorkspaces() {
        let logger = LoggingService.shared
        logger.info(
            "Export workspaces button clicked", service: "GeneralSettingsView",
            category: .workspaces)

        isProcessing = true

        // Get workspaces from the workspace service

        importExportService.exportWorkspaces(workspaceService.workspaces) { result in
            DispatchQueue.main.async {
                self.isProcessing = false

                switch result {
                case .success(let url):
                    logger.info(
                        "Workspaces exported successfully to \(url.path)",
                        service: "GeneralSettingsView", category: .workspaces)

                    // Show success toast
                    ToastManager.shared.showSuccess(
                        title: "Export Successful",
                        message: "Workspaces exported to \(url.lastPathComponent)",
                        duration: 3.0
                    )

                case .failure(let error):
                    if case .userCancelled = error {
                        logger.debug(
                            "Export cancelled by user", service: "GeneralSettingsView",
                            category: .workspaces)
                    } else {
                        logger.error(
                            "Export failed: \(error.localizedDescription)",
                            service: "GeneralSettingsView", category: .workspaces)

                        // Show error toast
                        ToastManager.shared.showError(
                            title: "Export Failed",
                            message: error.localizedDescription,
                            duration: 5.0
                        )
                    }
                }
            }
        }
    }

    private func importWorkspaces() {
        let logger = LoggingService.shared
        logger.info(
            "Import workspaces button clicked", service: "GeneralSettingsView",
            category: .workspaces)

        isProcessing = true

        importExportService.importWorkspaces { result in
            DispatchQueue.main.async {
                self.isProcessing = false

                switch result {
                case .success(let importResult):
                    logger.info(
                        "Workspaces imported successfully, checking for conflicts",
                        service: "GeneralSettingsView", category: .workspaces)

                    // Check for conflicts with existing workspaces

                    // Check for conflicts
                    let conflicts = self.importExportService.checkForConflicts(
                        importedWorkspaces: importResult.workspaces,
                        existingWorkspaces: workspaceService.workspaces
                    )

                    if conflicts.isEmpty {
                        // No conflicts, import directly
                        self.performImport(importResult.workspaces)
                    } else {
                        // Show conflict resolution dialog
                        self.pendingImportResult = importResult
                        self.conflicts = conflicts
                        self.showingConflictDialog = true
                    }

                case .failure(let error):
                    if case .userCancelled = error {
                        logger.debug(
                            "Import cancelled by user", service: "GeneralSettingsView",
                            category: .workspaces)
                    } else {
                        logger.error(
                            "Import failed: \(error.localizedDescription)",
                            service: "GeneralSettingsView", category: .workspaces)

                        // Show error toast
                        ToastManager.shared.showError(
                            title: "Import Failed",
                            message: error.localizedDescription,
                            duration: 5.0
                        )
                    }
                }
            }
        }
    }

    private func resolveConflicts(strategy: ConflictResolutionStrategy) {
        let logger = LoggingService.shared
        logger.info(
            "Resolving import conflicts with strategy: \(strategy)", service: "GeneralSettingsView",
            category: .workspaces)

        guard let importResult = pendingImportResult else {
            logger.error(
                "No pending import result found", service: "GeneralSettingsView",
                category: .workspaces)
            return
        }

        var workspacesToImport = importResult.workspaces

        switch strategy {
        case .overwrite:
            // Import all workspaces, overwriting existing ones
            logger.debug(
                "Overwriting existing workspaces", service: "GeneralSettingsView",
                category: .workspaces)

        case .rename:
            // Rename conflicting imported workspaces
            logger.debug(
                "Renaming conflicting workspaces", service: "GeneralSettingsView",
                category: .workspaces)
            workspacesToImport = renameConflictingWorkspaces(workspacesToImport)

        case .skip:
            // Skip conflicting workspaces
            logger.debug(
                "Skipping conflicting workspaces", service: "GeneralSettingsView",
                category: .workspaces)
            workspacesToImport = skipConflictingWorkspaces(workspacesToImport)
        }

        performImport(workspacesToImport)

        // Clear pending state
        pendingImportResult = nil
        conflicts = []
    }

    private func performImport(_ workspaces: [Workspace]) {
        let logger = LoggingService.shared
        logger.info(
            "Performing import of \(workspaces.count) workspaces", service: "GeneralSettingsView",
            category: .workspaces)

        for workspace in workspaces {
            workspaceService.addWorkspace(workspace)
        }

        // Show success toast
        ToastManager.shared.showSuccess(
            title: "Import Successful",
            message: "Imported \(workspaces.count) workspace(s)",
            duration: 3.0
        )

        logger.info(
            "Import completed successfully", service: "GeneralSettingsView", category: .workspaces)
    }

    private func renameConflictingWorkspaces(_ workspaces: [Workspace]) -> [Workspace] {
        let logger = LoggingService.shared

        var renamedWorkspaces: [Workspace] = []

        for workspace in workspaces {
            var newWorkspace = workspace

            // Check for name conflicts and rename if necessary
            if workspaceService.workspaces.contains(where: { $0.name == workspace.name }) {
                let originalName = workspace.name
                var counter = 1
                var newName = "\(originalName) (Imported)"

                while workspaceService.workspaces.contains(where: { $0.name == newName }) {
                    counter += 1
                    newName = "\(originalName) (Imported \(counter))"
                }

                newWorkspace.name = newName
                logger.debug(
                    "Renamed workspace '\(originalName)' to '\(newName)'",
                    service: "GeneralSettingsView", category: .workspaces)
            }

            // Clear shortcuts for conflicting workspaces to avoid shortcut conflicts
            if let keyCode = workspace.shortcutKeyCode,
                let modifiers = workspace.shortcutModifiers,
                workspaceService.workspaces.contains(where: {
                    $0.shortcutKeyCode == keyCode && $0.shortcutModifiers == modifiers
                })
            {
                newWorkspace.shortcutKeyCode = nil
                newWorkspace.shortcutModifiers = nil
                logger.debug(
                    "Cleared shortcut for workspace '\(newWorkspace.name)' due to conflict",
                    service: "GeneralSettingsView", category: .workspaces)
            }

            renamedWorkspaces.append(newWorkspace)
        }

        return renamedWorkspaces
    }

    private func skipConflictingWorkspaces(_ workspaces: [Workspace]) -> [Workspace] {
        let logger = LoggingService.shared
        let conflictingNames = Set(conflicts.map { $0.importedWorkspace.name })
        let nonConflictingWorkspaces = workspaces.filter { !conflictingNames.contains($0.name) }

        logger.debug(
            "Skipping \(workspaces.count - nonConflictingWorkspaces.count) conflicting workspaces",
            service: "GeneralSettingsView", category: .workspaces)

        return nonConflictingWorkspaces
    }

    // MARK: - Save Workspace Shortcut Conflict Detection

    private func checkSaveWorkspaceConflicts(shortcut: KeyboardShortcuts.Shortcut, showAlert: Bool)
    {
        // If "Allow any keyboard shortcut" is enabled, bypass validation
        if DefaultsManager.shared.allowAnyShortcut {
            // Clear the conflict state
            saveWorkspaceConflictingShortcut = nil
            return
        }

        // Convert to our Shortcut type
        guard let wrappedShortcut = ShortcutConverter.fromKeyboardShortcut(shortcut) else {
            return
        }

        // Use our wrapper to validate the shortcut
        let result = KeyboardShortcutsBridge.shared.validateShortcut(wrappedShortcut)

        if !result.isValid {
            // Set the conflicting shortcut name
            saveWorkspaceConflictingShortcut = result.conflictingApplication ?? "another shortcut"

            // Show an alert if requested
            if showAlert {
                showingSaveWorkspaceConflictAlert = true
            }
        } else {
            // Clear the conflict state
            saveWorkspaceConflictingShortcut = nil
        }
    }
}

#Preview {
    let snappingService = WindowSnappingService()
    let workspaceService = WorkspaceService(snappingService: snappingService)
    return SettingsView()
        .environmentObject(MockAppDelegate())
        .environmentObject(workspaceService)
}

private class MockAppDelegate: NSObject, NSApplicationDelegate, ObservableObject {
    func updateMenuBarIconVisibility() {}
}

// MARK: - Supporting Types

enum ConflictResolutionStrategy {
    case overwrite
    case rename
    case skip
}
